version: '3.8'
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
  runner:
    build:
      context: .
      dockerfile: Dockerfile.runner
    volumes:
      - ./actions-runner:/runner
    environment:
      - RUNNER_URL=https://github.com/rene-roid/cheff-up
      - RUNNER_TOKEN=AMRWQ7U3U32WLVC3ZM3NXYDI2VEMQ